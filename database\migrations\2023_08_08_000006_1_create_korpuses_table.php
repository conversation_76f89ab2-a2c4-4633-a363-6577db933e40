<?php

use App\Models\Korpus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('korpuses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bair_id')->constrained('bairs')->cascadeOnDelete();
            $table->string('name');
            $table->integer('order')->default(0);
            $table->integer('begin_toot_number')->default(0);
            $table->integer('end_toot_number')->default(0);
            $table->string(Korpus::CODE, 30)->nullable(); // CVSecurity integration code
            $table->timestamps();

            // Unique constraint for order within each bair
            $table->unique(['bair_id', 'order'], 'korpuses_bair_id_order_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('korpuses');
    }
};
