<?php

use App\Models\Davhar;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('davhars', function (Blueprint $table) {
            $table->id();
            $table->foreignId('orc_id')->constrained('orcs')->cascadeOnDelete();
            $table->string('number'); // Floor number (e.g., 4, 12, 16)
            $table->integer('order')->default(1); // Order within the Orc
            $table->integer('begin_toot_number')->default(0); // Starting door number for this floor
            $table->integer('end_toot_number')->default(0); // Ending door number for this floor
            $table->string('code', 30)->nullable(); // CVSecurity response code
            $table->timestamps();

            // Ensure unique order within each Orc
            $table->unique(['orc_id', 'order'], 'davhars_orc_id_order_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('davhars');
    }
};
