<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('toots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('korpus_id')->nullable()->constrained('korpuses')->cascadeOnDelete();
            $table->foreignId('davhar_id')->nullable()->constrained('davhars')->cascadeOnDelete();
            $table->bigInteger('number');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('toots');
    }
};
