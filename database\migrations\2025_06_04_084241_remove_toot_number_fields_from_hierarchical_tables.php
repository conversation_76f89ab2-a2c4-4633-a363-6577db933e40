<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Remove begin_toot_number and end_toot_number fields from korpuses, orcs, and davhars tables.
     * These fields are being removed as part of the hierarchical UI-based door numbering system
     * where door numbering will be handled through the UI configuration instead of stored ranges.
     */
    public function up(): void
    {
        // Remove begin_toot_number and end_toot_number from korpuses table
        Schema::table('korpuses', function (Blueprint $table) {
            $table->dropColumn(['begin_toot_number', 'end_toot_number']);
        });

        // Remove begin_toot_number and end_toot_number from orcs table
        Schema::table('orcs', function (Blueprint $table) {
            $table->dropColumn(['begin_toot_number', 'end_toot_number']);
        });

        // Remove begin_toot_number and end_toot_number from davhars table
        Schema::table('davhars', function (Blueprint $table) {
            $table->dropColumn(['begin_toot_number', 'end_toot_number']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * Re-add the begin_toot_number and end_toot_number fields if rollback is needed.
     */
    public function down(): void
    {
        // Re-add begin_toot_number and end_toot_number to korpuses table
        Schema::table('korpuses', function (Blueprint $table) {
            $table->integer('begin_toot_number')->default(0)->after('order');
            $table->integer('end_toot_number')->default(0)->after('begin_toot_number');
        });

        // Re-add begin_toot_number and end_toot_number to orcs table
        Schema::table('orcs', function (Blueprint $table) {
            $table->integer('begin_toot_number')->nullable()->after('number');
            $table->integer('end_toot_number')->nullable()->after('begin_toot_number');
        });

        // Re-add begin_toot_number and end_toot_number to davhars table
        Schema::table('davhars', function (Blueprint $table) {
            $table->integer('begin_toot_number')->default(0)->after('order');
            $table->integer('end_toot_number')->default(0)->after('begin_toot_number');
        });
    }
};
