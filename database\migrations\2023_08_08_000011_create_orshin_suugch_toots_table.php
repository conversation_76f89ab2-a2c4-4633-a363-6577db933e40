<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orshin_suugch_toots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('orshin_suugch_id')->constrained('orshin_suugches')->cascadeOnDelete();
            $table->unsignedBigInteger('korpus_id'); // Changed from bair_id and orc_id
            $table->string('number');
            $table->string('access_code')->nullable();
            $table->dateTime('ac_generated_date')->nullable();
            $table->string('state_bank_code')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orshin_suugch_toots');
    }
};
